<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backtrader MCP Service</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 900px; margin: 0 auto; padding: 20px; }
        h1, h2, h3, h4 { color: #333; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"], input[type="text"], input[type="password"], textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        button.delete-btn { background-color: #f44336; }
        button.delete-btn:hover { background-color: #da190b; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        .error { color: red; margin-top: 10px; }
        .tabs { display: flex; margin-bottom: 0; border-bottom: 1px solid #ddd; }
        .tab { padding: 10px 20px; cursor: pointer; border: 1px solid transparent; border-bottom: none; margin-bottom: -1px; }
        .tab.active { background-color: white; border-color: #ddd; border-bottom: 1px solid white; border-radius: 4px 4px 0 0; font-weight: bold; }
        .tab-content { display: none; border: 1px solid #ddd; padding: 20px; border-top: none; border-radius: 0 4px 4px 4px; }
        .tab-content.active { display: block; }
        .sub-tabs { display: flex; margin-bottom: 15px; border-bottom: 1px solid #eee; }
        .sub-tab { padding: 8px 15px; cursor: pointer; color: #007bff; }
        .sub-tab.active { font-weight: bold; border-bottom: 2px solid #007bff; }
        .sub-tab-content { display: none; }
        .sub-tab-content.active { display: block; }
        #user-files-list { list-style-type: none; padding: 0; }
        #user-files-list li { display: flex; justify-content: space-between; align-items: center; padding: 8px; border-bottom: 1px solid #eee; }
        #user-files-list li:last-child { border-bottom: none; }
        #user-files-list .file-actions button { margin-left: 10px; padding: 5px 10px; font-size: 0.9em; }
        .results { margin-top: 30px; border: 1px solid #ddd; padding: 20px; border-radius: 4px; display: none; }
        .modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); display: flex; justify-content: center; align-items: center; z-index: 1000; }
        .modal-content { background-color: white; padding: 30px; border-radius: 8px; width: 100%; max-width: 400px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
        .user-info { position: absolute; top: 20px; right: 20px; font-size: 0.9em; color: #555; }
        .user-info #logout-btn { margin-left: 10px; padding: 5px 10px; font-size: 0.9em; background-color: #f44336; }
        #data-analysis-results { margin-top: 20px; display: none; }
        .data-table-container { overflow-x: auto; }
        .data-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background-color: #f2f2f2; font-weight: bold; }
        .pagination-container { margin-top: 15px; display: flex; justify-content: space-between; align-items: center; }

        /* LLM聊天界面样式 */
        .chat-container { height: 500px; border: 1px solid #ddd; border-radius: 4px; display: flex; flex-direction: column; }
        .chat-messages { flex: 1; overflow-y: auto; padding: 15px; background-color: #f9f9f9; }
        .chat-message { margin-bottom: 15px; }
        .chat-message.user { text-align: right; }
        .chat-message.assistant { text-align: left; }
        .message-bubble { display: inline-block; max-width: 70%; padding: 10px 15px; border-radius: 18px; word-wrap: break-word; }
        .message-bubble.user { background-color: #007bff; color: white; }
        .message-bubble.assistant { background-color: white; border: 1px solid #ddd; }
        .chat-input-container { padding: 15px; border-top: 1px solid #ddd; background-color: white; }
        .chat-input-row { display: flex; gap: 10px; align-items: flex-end; }
        .chat-input { flex: 1; min-height: 40px; max-height: 120px; resize: vertical; }
        .chat-send-btn { padding: 10px 20px; white-space: nowrap; }
        .chat-mode-selector { margin-bottom: 10px; }
        .chat-mode-selector label { margin-right: 15px; font-weight: normal; }
        .typing-indicator { font-style: italic; color: #666; }
        .analysis-result { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-left: 4px solid #007bff; border-radius: 4px; }
        .analysis-section { margin-bottom: 10px; }
        .analysis-section h5 { margin: 0 0 5px 0; color: #333; }
        .analysis-section ul { margin: 5px 0; padding-left: 20px; }
        .risk-level { font-weight: bold; padding: 2px 8px; border-radius: 12px; font-size: 0.9em; }
        .risk-level.低风险 { background-color: #d4edda; color: #155724; }
        .risk-level.中等风险 { background-color: #fff3cd; color: #856404; }
        .risk-level.高风险 { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div id="login-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <h2>登录</h2>
            <form id="login-form">
                <div class="form-group"><label for="username">用户名:</label><input type="text" id="username" name="username" required></div>
                <div class="form-group"><label for="password">密码:</label><input type="password" id="password" name="password" required></div>
                <button type="submit">登录</button>
                <div id="login-error" class="error"></div>
            </form>
        </div>
    </div>

    <div id="app-content" style="display: none;">
        <div class="user-info"><span id="user-display"></span><button id="logout-btn">登出</button></div>
        <h1>Backtrader MCP Service</h1>
        <p>统一回测与数据分析平台</p>
        
        <div class="tabs">
            <div class="tab active" data-tab="file-upload">文件上传回测</div>
            <div class="tab" data-tab="integrated">集成回测</div>
            <div class="tab" data-tab="data-analysis">数据分析</div>
            <div class="tab" data-tab="llm-chat">LLM智能问答</div>
        </div>
        
        <form id="main-form">
            <div class="tab-content active" id="file-upload-tab">
                <!-- Content for File Upload Tab -->
            </div>
            
            <div class="tab-content" id="integrated-tab">
                <!-- Content for Integrated Backtest Tab -->
            </div>

            <div class="tab-content" id="data-analysis-tab">
                <h3>数据分析</h3>
                <div class="sub-tabs">
                    <div class="sub-tab active" data-subtab="da-fetch">数据获取 (API)</div>
                    <div class="sub-tab" data-subtab="da-browse">我的数据文件</div>
                </div>

            <div class="tab-content" id="llm-chat-tab">
                <h3>LLM智能问答</h3>
                <p>与AI助手对话，获取专业的金融数据分析和投资建议</p>

                <div class="chat-mode-selector">
                    <label><input type="radio" name="chat-mode" value="llm" checked> LLM智能模式 (深度分析)</label>
                    <label><input type="radio" name="chat-mode" value="rule"> 规则模式 (快速响应)</label>
                </div>

                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <div class="chat-message assistant">
                            <div class="message-bubble assistant">
                                👋 您好！我是您的AI金融分析助手。您可以问我关于股票分析、市场趋势、投资建议等问题。
                                <br><br>
                                <strong>示例问题：</strong>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    <li>分析000001平安银行最近的表现</li>
                                    <li>今日A股市场表现如何？</li>
                                    <li>000001和600519哪个投资价值更高？</li>
                                    <li>推荐一些科技股</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <div class="chat-input-row">
                            <textarea id="chat-input" class="chat-input" placeholder="请输入您的问题..." rows="2"></textarea>
                            <button id="chat-send-btn" class="chat-send-btn" type="button">发送</button>
                        </div>
                    </div>
                </div>
            </div>

                <div class="sub-tab-content active" id="da-fetch-content">
                    <p>直接调用 AkShare 的原始数据接口 (最大 10 MB)。</p>
                    <div class="form-group"><label for="da-interface">接口名称:</label><select id="da-interface"><option value="">加载中...</option></select></div>
                    <div class="form-group"><label for="da-params">接口参数 (JSON):</label><textarea id="da-params" rows="6"></textarea></div>
                    <button type="button" onclick="fetchDataForAnalysis(1)">获取数据</button>
                </div>

                <div class="sub-tab-content" id="da-browse-content">
                    <h4>上传新文件</h4>
                    <p>上传您自己的数据文件 (如 .csv) 进行浏览和分析 (最大 10 MB)。</p>
                    <div class="form-group"><label for="upload-data-file">选择文件:</label><input type="file" id="upload-data-file" accept=".csv"></div>
                    <button type="button" id="upload-btn">上传文件</button>
                    <hr style="margin: 20px 0;">
                    <h4>已上传文件</h4>
                    <div id="user-files-container">
                        <ul id="user-files-list"></ul>
                        <p id="no-files-msg" style="display: none;">您还没��上传任何文件。</p>
                    </div>
                </div>

                <div id="data-analysis-results">
                    <h4 id="results-header"></h4>
                    <div class="pagination-container" id="pagination-top"></div>
                    <div class="data-table-container"><table class="data-table" id="data-analysis-table"><thead></thead><tbody></tbody></table></div>
                    <div class="pagination-container" id="pagination-bottom"></div>
                </div>
            </div>

            <div id="backtest-common-form">
                <hr style="margin-top: 20px; margin-bottom: 20px;">
                <div class="form-group"><label for="benchmark-symbol">基准指数 (可选):</label><select id="benchmark-symbol" name="benchmark_symbol"><option value="">无</option><option value="000300">沪深300</option><option value="000001">上证指数</option></select></div>
                <div class="form-group"><label for="params">策略参数 (JSON, 可选):</label><textarea id="params" name="params" rows="4" placeholder='{"fast_period": 10, "slow_period": 30}'></textarea></div>
                <button type="submit">运行回测</button>
                <div id="form-error" class="error"></div>
            </div>
        </form>
        
        <div id="results" class="results"></div>
    </div>
    
    <script>
        const MAX_FILE_SIZE_BYTES = 10 * 1024 * 1024; // 10 MB
        let currentBrowserFilename = null;

        // --- API & Auth ---
        async function apiFetch(url, options = {}) {
            const token = localStorage.getItem('accessToken');
            const headers = { ...options.headers };
            if (token) { headers['Authorization'] = `Bearer ${token}`; }
            if (options.body && !(options.body instanceof FormData)) {
                headers['Content-Type'] = 'application/json';
            }
            const response = await fetch(url, { ...options, headers });
            if (response.status === 401) {
                localStorage.removeItem('accessToken');
                location.reload();
                throw new Error('Unauthorized');
            }
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: response.statusText }));
                throw new Error(errorData.detail || 'An unknown error occurred.');
            }
            if (response.status === 204) return null;
            return response.json();
        }
        async function checkAuth() {
            const token = localStorage.getItem('accessToken');
            if (!token) {
                document.getElementById('login-modal').style.display = 'flex';
                document.getElementById('app-content').style.display = 'none';
                return;
            }
            try {
                const user = await apiFetch('/api/users/me');
                document.getElementById('login-modal').style.display = 'none';
                document.getElementById('app-content').style.display = 'block';
                document.getElementById('user-display').textContent = `Welcome, ${user.username}`;
            } catch (error) {
                localStorage.removeItem('accessToken');
                location.reload();
            }
        }

        // --- File Management ---
        async function loadUserFiles() {
            const list = document.getElementById('user-files-list');
            const noFilesMsg = document.getElementById('no-files-msg');
            list.innerHTML = '<li>Loading...</li>';
            try {
                const files = await apiFetch('/api/data/files');
                list.innerHTML = '';
                if (files.length === 0) {
                    noFilesMsg.style.display = 'block';
                } else {
                    noFilesMsg.style.display = 'none';
                    files.forEach(filename => {
                        const li = document.createElement('li');
                        li.innerHTML = `
                            <span>${filename}</span>
                            <div class="file-actions">
                                <button onclick="browseStoredFile('${filename}', 1)">Browse</button>
                                <button class="delete-btn" onclick="deleteUserFile('${filename}')">Delete</button>
                            </div>
                        `;
                        list.appendChild(li);
                    });
                }
            } catch (error) {
                list.innerHTML = `<li><span class="error">Error loading files: ${error.message}</span></li>`;
            }
        }

        async function handleFileUpload() {
            const fileInput = document.getElementById('upload-data-file');
            if (fileInput.files.length === 0) {
                alert('Please select a file to upload.');
                return;
            }
            const file = fileInput.files[0];
            if (file.size > MAX_FILE_SIZE_BYTES) {
                alert(`File size exceeds the limit of ${MAX_FILE_SIZE_BYTES / 1024 / 1024} MB.`);
                return;
            }
            const formData = new FormData();
            formData.append('file', file);
            const uploadBtn = document.getElementById('upload-btn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Uploading...';
            try {
                await apiFetch('/api/data/upload', { method: 'POST', body: formData });
                fileInput.value = ''; // Clear the input
                await loadUserFiles();
            } catch (error) {
                alert(`Upload failed: ${error.message}`);
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload File';
            }
        }

        async function deleteUserFile(filename) {
            if (!confirm(`Are you sure you want to delete ${filename}?`)) return;
            try {
                await apiFetch(`/api/data/files/${filename}`, { method: 'DELETE' });
                await loadUserFiles();
            } catch (error) {
                alert(`Failed to delete file: ${error.message}`);
            }
        }

        // --- Data Analysis & Browsing ---
        async function fetchDataForAnalysis(page = 1) {
            const interfaceName = document.getElementById('da-interface').value;
            const paramsJson = document.getElementById('da-params').value;
            if (!interfaceName) {
                alert('Please select an interface.');
                return;
            }
            let params;
            try {
                params = JSON.parse(paramsJson);
            } catch (e) {
                alert('Invalid JSON in parameters.');
                return;
            }
            const requestBody = { interface: interfaceName, params: params, request_id: `req-${Date.now()}` };
            const url = `/api/mcp-data?page=${page}&page_size=20`;
            await executeDataRequest(url, { method: 'POST', body: JSON.stringify(requestBody) }, `API: ${interfaceName}`, page, fetchDataForAnalysis);
        }

        async function browseStoredFile(filename, page = 1) {
            currentBrowserFilename = filename;
            const url = `/api/data/explore/${filename}?page=${page}&page_size=20`;
            await executeDataRequest(url, { method: 'POST' }, `File: ${filename}`, page, browseStoredFile);
        }

        async function executeDataRequest(url, options, headerText, page, paginatorFn) {
            const resultsContainer = document.getElementById('data-analysis-results');
            resultsContainer.style.display = 'block';
            document.getElementById('results-header').textContent = `Results for ${headerText}`;
            resultsContainer.querySelector('.data-table-container').innerHTML = '<p>Loading data...</p>';
            try {
                const result = await apiFetch(url, options);
                if (result.error) throw new Error(result.error);
                renderDataTable(result.data);
                renderPagination(result.total_pages, result.current_page, result.total_records, paginatorFn);
            } catch (error) {
                resultsContainer.querySelector('.data-table-container').innerHTML = `<p class="error">An error occurred: ${error.message}</p>`;
            }
        }

        function renderDataTable(data) {
            const tableContainer = document.querySelector('#data-analysis-results .data-table-container');
            if (!data || data.length === 0) {
                tableContainer.innerHTML = '<p>No data returned.</p>';
                return;
            }
            const table = document.createElement('table');
            table.className = 'data-table';
            const thead = table.createTHead();
            const headerRow = thead.insertRow();
            Object.keys(data[0]).forEach(key => {
                const th = document.createElement('th');
                th.textContent = key;
                headerRow.appendChild(th);
            });
            const tbody = table.createTBody();
            data.forEach(item => {
                const row = tbody.insertRow();
                Object.values(item).forEach(text => {
                    const cell = row.insertCell();
                    cell.textContent = String(text !== null ? text : '');
                });
            });
            tableContainer.innerHTML = '';
            tableContainer.appendChild(table);
        }

        function renderPagination(totalPages, currentPage, totalRecords, paginatorFn) {
            const topPagination = document.getElementById('pagination-top');
            const bottomPagination = document.getElementById('pagination-bottom');
            [topPagination, bottomPagination].forEach(p => p.innerHTML = '');
            if (totalPages <= 1) return;

            const renderFnName = paginatorFn.name;
            const args = renderFnName === 'browseStoredFile' ? `'${currentBrowserFilename}', ` : '';

            let paginationHtml = `<div>Page ${currentPage} of ${totalPages} (${totalRecords} records)</div><div>`;
            if (currentPage > 1) { paginationHtml += `<button onclick="${renderFnName}(${args}${currentPage - 1})">Previous</button>`; }
            if (currentPage < totalPages) { paginationHtml += `<button onclick="${renderFnName}(${args}${currentPage + 1})">Next</button>`; }
            paginationHtml += `</div>`;
            [topPagination, bottomPagination].forEach(p => p.innerHTML = paginationHtml);
        }

        // --- Login Functionality ---
        async function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginError = document.getElementById('login-error');

            loginError.textContent = '';

            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch('/api/token', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: 'Login failed' }));
                    throw new Error(errorData.detail || 'Login failed');
                }

                const data = await response.json();
                localStorage.setItem('accessToken', data.access_token);

                // 登录成功，隐藏登录模态框，显示主应用
                document.getElementById('login-modal').style.display = 'none';
                document.getElementById('app-content').style.display = 'block';
                document.getElementById('user-display').textContent = `Welcome, ${username}`;

                // 加载初始数据
                loadInterfaceConfig();
                loadUserFiles();

            } catch (error) {
                loginError.textContent = error.message;
            }
        }

        function handleLogout() {
            localStorage.removeItem('accessToken');
            document.getElementById('login-modal').style.display = 'flex';
            document.getElementById('app-content').style.display = 'none';
            // 清空表单
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('login-error').textContent = '';
        }

        // --- Interface Configuration ---
        async function loadInterfaceConfig() {
            const interfaceSelect = document.getElementById('da-interface');
            try {
                const interfaces = await apiFetch('/api/mcp-data/interfaces');
                interfaceSelect.innerHTML = '<option value="">选择接口...</option>';
                interfaces.forEach(iface => {
                    const option = document.createElement('option');
                    option.value = iface.name;
                    option.textContent = `${iface.name} - ${iface.description || ''}`;
                    interfaceSelect.appendChild(option);
                });
            } catch (error) {
                interfaceSelect.innerHTML = '<option value="">加载接口失败</option>';
                console.error('Failed to load interfaces:', error);
            }
        }

        // --- Initial Load & Event Listeners ---
        document.addEventListener('DOMContentLoaded', () => {
            checkAuth();

            // 登录表单事件
            document.getElementById('login-form').addEventListener('submit', handleLogin);
            document.getElementById('logout-btn').addEventListener('click', handleLogout);

            // Main tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                    tab.classList.add('active');
                    document.getElementById(tab.dataset.tab + '-tab').classList.add('active');
                    const hideBacktestForm = (tab.dataset.tab === 'data-analysis' || tab.dataset.tab === 'llm-chat');
                    document.getElementById('backtest-common-form').style.display = hideBacktestForm ? 'none' : 'block';
                });
            });
            // Data analysis sub-tabs
            document.querySelectorAll('#data-analysis-tab .sub-tab').forEach(subTab => {
                subTab.addEventListener('click', () => {
                    document.querySelectorAll('#data-analysis-tab .sub-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('#data-analysis-tab .sub-tab-content').forEach(c => c.classList.remove('active'));
                    subTab.classList.add('active');
                    document.getElementById(subTab.dataset.subtab + '-content').classList.add('active');
                    document.getElementById('data-analysis-results').style.display = 'none';
                    if (subTab.dataset.subtab === 'da-browse') {
                        loadUserFiles();
                    }
                });
            });
            // Other listeners
            document.getElementById('upload-btn').addEventListener('click', handleFileUpload);

            // LLM聊天功能
            initializeLLMChat();

            // Load initial data will be loaded after successful login
            document.getElementById('file-upload-tab').innerHTML = `...`; // Populate as before
            document.getElementById('integrated-tab').innerHTML = `...`; // Populate as before
        });

        // LLM聊天功能实现
        function initializeLLMChat() {
            const chatInput = document.getElementById('chat-input');
            const chatSendBtn = document.getElementById('chat-send-btn');
            const chatMessages = document.getElementById('chat-messages');

            // 发送按钮点击事件
            chatSendBtn.addEventListener('click', sendChatMessage);

            // 回车键发送消息
            chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendChatMessage();
                }
            });
        }

        async function sendChatMessage() {
            const chatInput = document.getElementById('chat-input');
            const chatSendBtn = document.getElementById('chat-send-btn');
            const chatMessages = document.getElementById('chat-messages');
            const message = chatInput.value.trim();

            if (!message) return;

            // 添加用户消息
            addChatMessage('user', message);
            chatInput.value = '';
            chatSendBtn.disabled = true;

            // 显示输入指示器
            const typingIndicator = addChatMessage('assistant', '<div class="typing-indicator">AI正在思考中...</div>');

            try {
                // 获取选择的模式
                const useLLM = document.querySelector('input[name="chat-mode"]:checked').value === 'llm';

                // 发送请求
                const endpoint = useLLM ? '/api/llm/chat' : '/api/llm/analyze?use_llm=false';
                const requestBody = useLLM ? { prompt: message } : { query: message };

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 移除输入指示器
                typingIndicator.remove();

                // 显示AI响应
                if (useLLM && data.response) {
                    // LLM聊天模式的简单响应
                    addChatMessage('assistant', data.response);
                } else if (!useLLM && data.summary) {
                    // 规则模式的结构化响应
                    const analysisHtml = formatAnalysisResult(data);
                    addChatMessage('assistant', analysisHtml);
                } else {
                    addChatMessage('assistant', '抱歉，我无法理解您的问题，请尝试重新表述。');
                }

            } catch (error) {
                console.error('聊天请求失败:', error);
                typingIndicator.remove();
                addChatMessage('assistant', `抱歉，发生了错误：${error.message}`);
            } finally {
                chatSendBtn.disabled = false;
            }
        }

        function addChatMessage(sender, content) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${sender}`;

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = `message-bubble ${sender}`;
            bubbleDiv.innerHTML = content;

            messageDiv.appendChild(bubbleDiv);
            chatMessages.appendChild(messageDiv);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        function formatAnalysisResult(data) {
            let html = `<div class="analysis-result">`;

            if (data.summary) {
                html += `<div class="analysis-section"><h5>📊 分析摘要</h5><p>${data.summary}</p></div>`;
            }

            if (data.insights && data.insights.length > 0) {
                html += `<div class="analysis-section"><h5>💡 关键洞察</h5><ul>`;
                data.insights.forEach(insight => {
                    html += `<li>${insight}</li>`;
                });
                html += `</ul></div>`;
            }

            if (data.recommendations && data.recommendations.length > 0) {
                html += `<div class="analysis-section"><h5>💼 投资建议</h5><ul>`;
                data.recommendations.forEach(rec => {
                    html += `<li>${rec}</li>`;
                });
                html += `</ul></div>`;
            }

            if (data.risk_level) {
                html += `<div class="analysis-section"><h5>⚠️ 风险等级</h5><span class="risk-level ${data.risk_level}">${data.risk_level}</span></div>`;
            }

            if (data.confidence) {
                html += `<div class="analysis-section"><h5>🎯 置信度</h5><p>${(data.confidence * 100).toFixed(1)}%</p></div>`;
            }

            html += `</div>`;
            return html;
        }
    </script>
</body>
</html>
