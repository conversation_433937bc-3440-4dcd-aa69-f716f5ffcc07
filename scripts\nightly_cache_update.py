#!/usr/bin/env python3
"""
夜间数据缓存更新脚本
每天凌晨自动更新常用的金融数据到本地缓存
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from adaptors.akshare import AKShareAdaptor
from core.mcp_protocol import MCPRequest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("nightly_update.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("nightly-cache-update")

class NightlyCacheUpdater:
    def __init__(self):
        self.adaptor = AKShareAdaptor(cache_dir="static/cache/system")
        self.update_config = self._load_update_config()
        
    def _load_update_config(self):
        """加载需要更新的数据配置"""
        config_file = Path("scripts/nightly_update_config.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 默认配置
            return {
                "daily_updates": [
                    {
                        "interface": "stock_zh_a_spot_em",
                        "params": {},
                        "description": "A股实时行情"
                    },
                    {
                        "interface": "fund_etf_spot_em", 
                        "params": {},
                        "description": "ETF实时行情"
                    },
                    {
                        "interface": "index_zh_a_hist",
                        "params": {
                            "symbol": "000001",
                            "period": "daily",
                            "start_date": (datetime.now() - timedelta(days=30)).strftime("%Y%m%d"),
                            "end_date": datetime.now().strftime("%Y%m%d")
                        },
                        "description": "上证指数近30天数据"
                    },
                    {
                        "interface": "index_zh_a_hist",
                        "params": {
                            "symbol": "000300",
                            "period": "daily", 
                            "start_date": (datetime.now() - timedelta(days=30)).strftime("%Y%m%d"),
                            "end_date": datetime.now().strftime("%Y%m%d")
                        },
                        "description": "沪深300指数近30天数据"
                    }
                ],
                "weekly_updates": [
                    {
                        "interface": "stock_zh_a_hist",
                        "params": {
                            "symbol": "000001",
                            "period": "daily",
                            "start_date": (datetime.now() - timedelta(days=365)).strftime("%Y%m%d"),
                            "end_date": datetime.now().strftime("%Y%m%d")
                        },
                        "description": "平安银行年度历史数据"
                    }
                ]
            }
    
    async def update_single_interface(self, interface_config):
        """更新单个接口数据"""
        interface = interface_config["interface"]
        params = interface_config["params"]
        description = interface_config.get("description", interface)
        
        try:
            logger.info(f"开始更新: {description} ({interface})")
            
            # 强制更新缓存（忽略现有缓存）
            result = await self.adaptor.call(interface, **params)
            
            if result is not None:
                logger.info(f"✅ 成功更新: {description}")
                return True
            else:
                logger.warning(f"⚠️ 数据为空: {description}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 更新失败: {description} - {str(e)}")
            return False
    
    async def run_daily_updates(self):
        """执行每日更新"""
        logger.info("=== 开始每日数据更新 ===")
        
        daily_configs = self.update_config.get("daily_updates", [])
        success_count = 0
        total_count = len(daily_configs)
        
        for config in daily_configs:
            success = await self.update_single_interface(config)
            if success:
                success_count += 1
            
            # 避免请求过于频繁
            await asyncio.sleep(2)
        
        logger.info(f"=== 每日更新完成: {success_count}/{total_count} 成功 ===")
        return success_count, total_count
    
    async def run_weekly_updates(self):
        """执行每周更新（仅周日执行）"""
        if datetime.now().weekday() != 6:  # 0=Monday, 6=Sunday
            logger.info("今天不是周日，跳过周更新")
            return 0, 0
            
        logger.info("=== 开始每周数据更新 ===")
        
        weekly_configs = self.update_config.get("weekly_updates", [])
        success_count = 0
        total_count = len(weekly_configs)
        
        for config in weekly_configs:
            success = await self.update_single_interface(config)
            if success:
                success_count += 1
            
            # 周更新间隔更长
            await asyncio.sleep(5)
        
        logger.info(f"=== 每周更新完成: {success_count}/{total_count} 成功 ===")
        return success_count, total_count
    
    async def cleanup_old_cache(self, days_to_keep=30):
        """清理过期缓存文件"""
        logger.info(f"=== 开始清理 {days_to_keep} 天前的缓存文件 ===")
        
        cache_dir = Path("static/cache/system")
        if not cache_dir.exists():
            logger.info("缓存目录不存在，跳过清理")
            return
        
        cutoff_time = datetime.now() - timedelta(days=days_to_keep)
        deleted_count = 0
        
        for cache_file in cache_dir.glob("*.parquet"):
            try:
                file_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
                if file_time < cutoff_time:
                    cache_file.unlink()
                    deleted_count += 1
                    logger.debug(f"删除过期缓存: {cache_file.name}")
            except Exception as e:
                logger.warning(f"删除缓存文件失败 {cache_file.name}: {e}")
        
        logger.info(f"=== 缓存清理完成: 删除了 {deleted_count} 个过期文件 ===")
    
    async def run_full_update(self):
        """执行完整的夜间更新流程"""
        start_time = datetime.now()
        logger.info(f"🌙 夜间缓存更新开始: {start_time}")
        
        try:
            # 1. 每日更新
            daily_success, daily_total = await self.run_daily_updates()
            
            # 2. 每周更新
            weekly_success, weekly_total = await self.run_weekly_updates()
            
            # 3. 清理过期缓存
            await self.cleanup_old_cache()
            
            # 4. 总结
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info(f"🎉 夜间更新完成!")
            logger.info(f"   耗时: {duration}")
            logger.info(f"   每日更新: {daily_success}/{daily_total}")
            logger.info(f"   每周更新: {weekly_success}/{weekly_total}")
            
            return True
            
        except Exception as e:
            logger.error(f"💥 夜间更新失败: {str(e)}", exc_info=True)
            return False

async def main():
    """主函数"""
    updater = NightlyCacheUpdater()
    success = await updater.run_full_update()
    
    # 退出码：0表示成功，1表示失败
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
